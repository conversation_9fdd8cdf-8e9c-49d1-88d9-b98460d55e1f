<?php
// api应用路由定义文件

use think\facade\Route;

// 测试路由
Route::get('test/bean', 'Test/bean');
Route::get('test/service', 'Test/service');
Route::get('test/create-table', 'Test/createTable');
Route::get('test/create-category-data', 'Test/createCategoryData');
Route::get('test/category', 'Test/category');

// 管理员认证路由（不需要认证的接口）
Route::group('admin/auth', function () {
    Route::post('login', 'admin.AuthController/login');           // 登录
    Route::get('check', 'admin.AuthController/check');            // 检查登录状态
});

// 管理员认证路由（需要认证的接口）
Route::group('admin/auth', function () {
    Route::post('logout', 'admin.AuthController/logout');         // 登出
    Route::get('info', 'admin.AuthController/info');              // 获取用户信息
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 管理员用户管理路由（需要认证和权限）
Route::group('admin/users', function () {
    Route::get('', 'admin.UserController/index');                 // 用户列表
    Route::post('', 'admin.UserController/create');               // 创建用户
    Route::get(':id', 'admin.UserController/read');               // 获取用户详情
    Route::put(':id', 'admin.UserController/update');             // 更新用户
    Route::delete(':id', 'admin.UserController/delete');          // 删除用户
    Route::post(':id/password', 'admin.UserController/changePassword'); // 修改密码
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 文章分类管理路由（使用方法级权限控制）
Route::group('admin/categories', function () {
    Route::get('', 'admin.ArticleCategoryController/index');      // 分类列表
    Route::post('', 'admin.ArticleCategoryController/create');    // 创建分类
    Route::get('tree', 'admin.ArticleCategoryController/tree');   // 获取菜单树
    Route::get('children', 'admin.ArticleCategoryController/children'); // 获取子分类
    Route::get('type/:type', 'admin.ArticleCategoryController/getByType'); // 根据类型获取分类
    Route::get(':id', 'admin.ArticleCategoryController/read');    // 获取分类详情
    Route::put(':id', 'admin.ArticleCategoryController/update');  // 更新分类
    Route::delete(':id', 'admin.ArticleCategoryController/delete'); // 删除分类
    Route::get(':id/breadcrumb', 'admin.ArticleCategoryController/breadcrumb'); // 获取面包屑
    Route::post(':id/move', 'admin.ArticleCategoryController/move'); // 移动分类
    Route::post(':id/toggle-show', 'admin.ArticleCategoryController/toggleShow'); // 切换显示状态
    Route::post('update-sort', 'admin.ArticleCategoryController/updateSort'); // 批量更新排序
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// 公开分类接口（演示不同权限级别）
Route::group('categories', function () {
    Route::get('public/tree', 'PublicCategoryController/publicTree');     // 公开菜单树（无需权限）
    Route::get('public/list', 'PublicCategoryController/publicList');     // 公开分类列表（无需权限）
    Route::get('admin/stats', 'PublicCategoryController/adminStats');     // 管理员统计（需要登录）
    Route::post('system/manage', 'PublicCategoryController/systemManage'); // 系统管理（需要超级管理员）
})->middleware([
    \app\api\middleware\AuthMiddleware::class,
    \app\api\middleware\RoleMiddleware::class
]);

// API基础路由
Route::get('/', 'Index/index');
Route::get('test', 'Index/test');
